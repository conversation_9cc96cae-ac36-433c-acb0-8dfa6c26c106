import { But<PERSON> } from "@/components/ui/button"
import { ModeToggle } from "@/components/mode-toggle"
import planfulyLogo from '/logo/Planfuly_Logo.png'
import { Heading1, SmallText } from "@/components/ui/type"

function MainPage() {
  return (
    <div className="relative">
      <div className="absolute top-4 right-4">
        <ModeToggle />
      </div>

      <div>
        <img src={planfulyLogo} className="logo" alt="Planfuly logo" />
      </div>
      <Heading1>Engage Planfuly</Heading1>

      <div className="flex min-h-svh flex-col items-center justify-center">
        <Button>Click me</Button>
      </div>

      <SmallText className="footer">Planfuly Inc.</SmallText>
    </div>
  )
}

export default MainPage
