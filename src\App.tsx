import { ThemeProvider } from "@/components/theme-provider"
import { BrowserRouter as Router, Routes, Route } from "react-router-dom"
import MainPage from "@/pages/MainPage"

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="planfuly-ui-theme">
      <Router>
        <Routes>
          <Route path="/" element={<MainPage />} />
        </Routes>
      </Router>
    </ThemeProvider>
  )
}

export default App
